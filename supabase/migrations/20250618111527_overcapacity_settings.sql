create table "public"."employee_capacity_settings" (
    "id" uuid not null,
    "overcapacity_day_with_target_hours_h" real not null default '2'::real,
    "overcapacity_day_with_target_hours_and_pto_vacation_h" real not null default '0'::real,
    "overcapacity_day_with_target_hours_and_pto_sick_h" real not null default '0'::real,
    "overcapacity_day_with_target_hours_and_pto_compensation_h" real not null default '0'::real,
    "overcapacity_day_with_target_hours_and_pto_training_h" real not null default '0'::real,
    "overcapacity_day_with_target_hours_and_pto_military_cs_h" real not null default '0'::real,
    "overcapacity_day_with_target_hours_and_pto_accident_h" real not null default '0'::real,
    "overcapacity_day_with_target_hours_and_pto_parental_leave_h" real not null default '0'::real,
    "overcapacity_day_with_target_hours_and_pto_holiday_h" real not null default '0'::real,
    "overcapacity_day_with_target_hours_and_pto_other_h" real not null default '0'::real,
    "overcapacity_day_with_target_hours_and_pto_freetime_h" real not null default '0'::real,
    "overcapacity_day_without_target_hours_h" real not null default '0'::real,
    "overcapacity_week_h" real not null default '8'::real,
    "warning_day_active" boolean not null default true,
    "warning_week_active" boolean not null default true
);


alter table "public"."employee_capacity_settings" enable row level security;

CREATE UNIQUE INDEX employee_capacity_settings_pkey ON public.employee_capacity_settings USING btree (id);

alter table "public"."employee_capacity_settings" add constraint "employee_capacity_settings_pkey" PRIMARY KEY using index "employee_capacity_settings_pkey";

alter table "public"."employee_capacity_settings" add constraint "employee_capacity_settings_id_fkey" FOREIGN KEY (id) REFERENCES employees(user_id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."employee_capacity_settings" validate constraint "employee_capacity_settings_id_fkey";

grant delete on table "public"."employee_capacity_settings" to "anon";

grant insert on table "public"."employee_capacity_settings" to "anon";

grant references on table "public"."employee_capacity_settings" to "anon";

grant select on table "public"."employee_capacity_settings" to "anon";

grant trigger on table "public"."employee_capacity_settings" to "anon";

grant truncate on table "public"."employee_capacity_settings" to "anon";

grant update on table "public"."employee_capacity_settings" to "anon";

grant delete on table "public"."employee_capacity_settings" to "authenticated";

grant insert on table "public"."employee_capacity_settings" to "authenticated";

grant references on table "public"."employee_capacity_settings" to "authenticated";

grant select on table "public"."employee_capacity_settings" to "authenticated";

grant trigger on table "public"."employee_capacity_settings" to "authenticated";

grant truncate on table "public"."employee_capacity_settings" to "authenticated";

grant update on table "public"."employee_capacity_settings" to "authenticated";

grant delete on table "public"."employee_capacity_settings" to "service_role";

grant insert on table "public"."employee_capacity_settings" to "service_role";

grant references on table "public"."employee_capacity_settings" to "service_role";

grant select on table "public"."employee_capacity_settings" to "service_role";

grant trigger on table "public"."employee_capacity_settings" to "service_role";

grant truncate on table "public"."employee_capacity_settings" to "service_role";

grant update on table "public"."employee_capacity_settings" to "service_role";

create policy "Allow all for auth"
on "public"."employee_capacity_settings"
as permissive
for all
to authenticated
using (true)
with check (true);




