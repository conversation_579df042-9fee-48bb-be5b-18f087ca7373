import { ClickUpTask } from '@/data/types/clickup.types';

/**
 * Extracts the PTO type name from a ClickUp task's custom fields.
 * 
 * @param task - The ClickUp task to extract PTO type from
 * @returns The PTO type name if found, false otherwise
 * 
 * @example
 * ```typescript
 * const task = { ... }; // ClickUp task with PTO Typ custom field
 * const ptoType = getPTOTypeName(task);
 * if (ptoType) {
 *   console.log(`PTO Type: ${ptoType}`); // e.g., "Ferien", "Krankheit", "Freizeit"
 * }
 * ```
 */
export function getPTOTypeName(task: ClickUpTask): string | false {
  if (!task.custom_fields) {
    return false;
  }

  // Find the PTO Typ custom field
  const ptoField = task.custom_fields.find((field) => field.name === 'PTO Typ');
  
  if (!ptoField) {
    return false;
  }

  // Get the options and current value index
  const ptoOptions = ptoField.type_config?.options;
  const ptoTypeIndex = ptoField.value;

  if (!ptoOptions || ptoTypeIndex === undefined || ptoTypeIndex === null) {
    return false;
  }

  // Find the option that matches the current value index
  const selectedOption = ptoOptions.find(
    (option) => Number(option.orderindex) === Number(ptoTypeIndex)
  );

  return selectedOption?.name || false;
}
