export const BexioInvoiceStatus = {
  7: 'draft',
  8: 'pending',
  9: 'paid',
  16: 'partial',
  19: 'canceled',
  31: 'unpaid',
};

const overcapacityLabels = {
  overcapacity_day_with_target_hours_h: 'Überkapazität pro Tag mit Sollzeit [h]',
  overcapacity_day_with_target_hours_and_pto_vacation_h: 'Überkapazität pro Tag mit Sollzeit & PTO-Typ Ferien [h]',
  overcapacity_day_with_target_hours_and_pto_sick_h: 'Überkapazität pro Tag mit Sollzeit & PTO-Typ Krankheit [h]',
  overcapacity_day_with_target_hours_and_pto_compensation_h:
    'Überkapazität pro Tag mit Sollzeit & PTO-Typ Kompensation [h]',
  overcapacity_day_with_target_hours_and_pto_training_h:
    'Überkapazität pro Tag mit Sollzeit & PTO-Typ Wei<PERSON>bildung [h]',
  overcapacity_day_with_target_hours_and_pto_military_cs_h:
    'Überkapazität pro Tag mit Sollzeit & PTO-Typ Mi<PERSON>är/ZS [h]',
  overcapacity_day_with_target_hours_and_pto_accident_h: 'Überkapazität pro Tag mit Sollzeit & PTO-Typ Unfall [h]',
  overcapacity_day_with_target_hours_and_pto_parental_leave_h:
    'Überkapazität pro Tag mit Sollzeit & PTO-Typ Mutterschaft/Vaterschaft [h]',
  overcapacity_day_with_target_hours_and_pto_holiday_h: 'Überkapazität pro Tag mit Sollzeit & PTO-Typ Feiertag [h]',
  overcapacity_day_with_target_hours_and_pto_other_h: 'Überkapazität pro Tag mit Sollzeit & PTO-Typ Sonstige [h]',
  overcapacity_day_with_target_hours_and_pto_freetime_h: 'Überkapazität pro Tag mit Sollzeit & PTO-Typ Freizeit [h]',
  overcapacity_day_without_target_hours_h: 'Überkapazität pro Tag ohne Sollzeit [h]',
  overcapacity_week_h: 'Überkapazität pro Woche [h]',
  warning_day_active: 'Warnung pro Tag',
  warning_week_active: 'Warnung pro Woche',
} as const;

export type OvercapacityRuleKey = keyof typeof overcapacityLabels;
