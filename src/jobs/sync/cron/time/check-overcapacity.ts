import { cronTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { convertCronToUTC } from '@/lib/utils';

export const checkOvercapacity = client.defineJob({
  id: 'check-overcapacity',
  name: 'Check Overcapacity',
  version: '1.0.0',
  trigger: cronTrigger({ cron: convertCronToUTC('0 15 * * *') }), // every day at 15:00
  run: async (payload, io, _) => {
    const overcapacitySettings = await io.runTask('fetch-overcapacity-settings', async () => {
      const supabase = createClient();
      const { data, error } = await supabase.from('employee_capacity_settings').select('*');

      if (error) {
        throw new Error(`Error fetching overcapacity settings: ${JSON.stringify(error)}`);
      }

      return data;
    });

    const estimatedOpenTasks = await io.runTask('fetch-estimated-tasks-next-180-days', async () => {
      const supabase = createClient();
      // fetch all open tasks with clickup_time_estimate > 0 and clickup_due_date in the next 180 days
      const { data, error } = await supabase
        .from('tasks')
        .select(
          'clickup_task_id, clickup_time_estimate, clickup_due_date, projects(name), employees(name, user_id, clickup_user_id)',
        )
        .gt('clickup_time_estimate', 0)
        .is('clickup_date_closed', null)
        .gte('clickup_due_date', new Date().getTime())
        .lte('clickup_due_date', new Date(new Date().setDate(new Date().getDate() + 180)).getTime());

      if (error) {
        throw new Error(`Error fetching tasks: ${JSON.stringify(error)}`);
      }

      /*
      2.Tagesauswertung (pro Mitarbeitendem pro Tag)
          Berechne:
          Summe aller geschätzten Zeiten pro Tag
          Anzahl der Tage, die eine Person durch Tasks belastet ist
          Berücksichtige Tasks mit Laufzeit über mehrere Tage anteilig verteilt

          startDate is always at the start of the due date
          endDate is always at the start of due date + time estimate

          group by employees and day, we want the sum of the time estimates for each day
       */

      return data?.reduce(
        (acc, curr) => {
          const startDate = new Date(curr.clickup_due_date!);
          const endDate = new Date(curr.clickup_due_date! + curr.clickup_time_estimate!);
          const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
          for (let i = 0; i < days; i++) {
            const date = new Date(startDate.getTime() + i * (1000 * 60 * 60 * 24));
            const dateStr = date.toISOString().slice(0, 10);
            if (!acc[dateStr]) {
              acc[dateStr] = {};
            }
            const name = String(curr.employees?.name);
            if (!acc[dateStr][name]) {
              acc[dateStr][name] = 0;
            }
            acc[dateStr][name] += curr.clickup_time_estimate! / days / 1000 / 60 / 60;
          }
          return acc;
        },
        {} as { [key: string]: { [key: string]: number } },
      );
    });
  },
});
