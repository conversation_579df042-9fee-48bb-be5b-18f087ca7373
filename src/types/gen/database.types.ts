export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      bexio_contact_sectors: {
        Row: {
          id: number
          name: string
        }
        Insert: {
          id: number
          name: string
        }
        Update: {
          id?: number
          name?: string
        }
        Relationships: []
      }
      bexio_invoices: {
        Row: {
          contact_id: number | null
          document_nr: string
          id: number
          project_id: number | null
          services_amount: number | null
          status: string
          total: number
          total_gross: number
          total_net: number
          total_received_payments: number
          total_remaining_payments: number
          total_taxes: number
          trades_amount: number | null
          valid_from: string | null
          valid_to: string | null
        }
        Insert: {
          contact_id?: number | null
          document_nr: string
          id: number
          project_id?: number | null
          services_amount?: number | null
          status?: string
          total: number
          total_gross: number
          total_net: number
          total_received_payments: number
          total_remaining_payments: number
          total_taxes: number
          trades_amount?: number | null
          valid_from?: string | null
          valid_to?: string | null
        }
        Update: {
          contact_id?: number | null
          document_nr?: string
          id?: number
          project_id?: number | null
          services_amount?: number | null
          status?: string
          total?: number
          total_gross?: number
          total_net?: number
          total_received_payments?: number
          total_remaining_payments?: number
          total_taxes?: number
          trades_amount?: number | null
          valid_from?: string | null
          valid_to?: string | null
        }
        Relationships: []
      }
      bexio_projects: {
        Row: {
          bexio_client_id: number | null
          bexio_effective_cost: number | null
          bexio_id: number
          bexio_user_id: number | null
          budget: number | null
          client_name: string | null
          close_date: string | null
          contact_person: string | null
          difference_budget: number | null
          difference_budget_percentage: number | null
          difference_estimated_cost: number | null
          difference_estimated_cost_percentage: number | null
          difference_time: number | null
          difference_time_percentage: number | null
          effective_cost: number | null
          end_date: string | null
          estimated_cost: number | null
          factorized_amount: number | null
          invoice_ids: number[] | null
          last_temp_unarchive: string | null
          name: string | null
          nr: string | null
          progress_percentage: number | null
          services_amount: number | null
          start_date: string | null
          status: string | null
          substatus: string | null
          time_estimate: number | null
          time_estimate_open: number | null
          time_spent: number | null
          trades_amount: number | null
        }
        Insert: {
          bexio_client_id?: number | null
          bexio_effective_cost?: number | null
          bexio_id: number
          bexio_user_id?: number | null
          budget?: number | null
          client_name?: string | null
          close_date?: string | null
          contact_person?: string | null
          difference_budget?: number | null
          difference_budget_percentage?: number | null
          difference_estimated_cost?: number | null
          difference_estimated_cost_percentage?: number | null
          difference_time?: number | null
          difference_time_percentage?: number | null
          effective_cost?: number | null
          end_date?: string | null
          estimated_cost?: number | null
          factorized_amount?: number | null
          invoice_ids?: number[] | null
          last_temp_unarchive?: string | null
          name?: string | null
          nr?: string | null
          progress_percentage?: number | null
          services_amount?: number | null
          start_date?: string | null
          status?: string | null
          substatus?: string | null
          time_estimate?: number | null
          time_estimate_open?: number | null
          time_spent?: number | null
          trades_amount?: number | null
        }
        Update: {
          bexio_client_id?: number | null
          bexio_effective_cost?: number | null
          bexio_id?: number
          bexio_user_id?: number | null
          budget?: number | null
          client_name?: string | null
          close_date?: string | null
          contact_person?: string | null
          difference_budget?: number | null
          difference_budget_percentage?: number | null
          difference_estimated_cost?: number | null
          difference_estimated_cost_percentage?: number | null
          difference_time?: number | null
          difference_time_percentage?: number | null
          effective_cost?: number | null
          end_date?: string | null
          estimated_cost?: number | null
          factorized_amount?: number | null
          invoice_ids?: number[] | null
          last_temp_unarchive?: string | null
          name?: string | null
          nr?: string | null
          progress_percentage?: number | null
          services_amount?: number | null
          start_date?: string | null
          status?: string | null
          substatus?: string | null
          time_estimate?: number | null
          time_estimate_open?: number | null
          time_spent?: number | null
          trades_amount?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "public_bexio_projects_bexio_user_id_fkey"
            columns: ["bexio_user_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["bexio_user_id"]
          },
        ]
      }
      business_activities: {
        Row: {
          bexio_business_activity_id: number
          bexio_business_activity_name: string
          billable: boolean
          clickup_tag_bg: string | null
          clickup_tag_fg: string | null
          clickup_task_tag: string
          hourly_rate: number
        }
        Insert: {
          bexio_business_activity_id: number
          bexio_business_activity_name: string
          billable?: boolean
          clickup_tag_bg?: string | null
          clickup_tag_fg?: string | null
          clickup_task_tag: string
          hourly_rate?: number
        }
        Update: {
          bexio_business_activity_id?: number
          bexio_business_activity_name?: string
          billable?: boolean
          clickup_tag_bg?: string | null
          clickup_tag_fg?: string | null
          clickup_task_tag?: string
          hourly_rate?: number
        }
        Relationships: []
      }
      clients: {
        Row: {
          bexio_contact_id: number | null
          bexio_sector_ids: number[]
          clickup_archived: boolean
          clickup_folder_assignee_id: string | null
          clickup_folder_id: string | null
          dropbox_folder_path: string | null
          id: number
          name: string
          num_employees: number | null
        }
        Insert: {
          bexio_contact_id?: number | null
          bexio_sector_ids?: number[]
          clickup_archived?: boolean
          clickup_folder_assignee_id?: string | null
          clickup_folder_id?: string | null
          dropbox_folder_path?: string | null
          id?: number
          name: string
          num_employees?: number | null
        }
        Update: {
          bexio_contact_id?: number | null
          bexio_sector_ids?: number[]
          clickup_archived?: boolean
          clickup_folder_assignee_id?: string | null
          clickup_folder_id?: string | null
          dropbox_folder_path?: string | null
          id?: number
          name?: string
          num_employees?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "clients_clickup_folder_assignee_id_fkey"
            columns: ["clickup_folder_assignee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["clickup_user_id"]
          },
        ]
      }
      custom_filters: {
        Row: {
          created_at: string
          filter: Json[]
          id: number
          name: string
          type: string
        }
        Insert: {
          created_at?: string
          filter?: Json[]
          id?: number
          name: string
          type?: string
        }
        Update: {
          created_at?: string
          filter?: Json[]
          id?: number
          name?: string
          type?: string
        }
        Relationships: []
      }
      delete_log: {
        Row: {
          created_at: string
          delete_confirmed: boolean
          entity_id: string
          id: string
          type: string
        }
        Insert: {
          created_at?: string
          delete_confirmed?: boolean
          entity_id: string
          id?: string
          type: string
        }
        Update: {
          created_at?: string
          delete_confirmed?: boolean
          entity_id?: string
          id?: string
          type?: string
        }
        Relationships: []
      }
      employee_capacity_settings: {
        Row: {
          id: string
          overcapacity_day_with_target_hours_and_pto_accident_h: number
          overcapacity_day_with_target_hours_and_pto_compensation_h: number
          overcapacity_day_with_target_hours_and_pto_freetime_h: number
          overcapacity_day_with_target_hours_and_pto_holiday_h: number
          overcapacity_day_with_target_hours_and_pto_military_cs_h: number
          overcapacity_day_with_target_hours_and_pto_other_h: number
          overcapacity_day_with_target_hours_and_pto_parental_leave_h: number
          overcapacity_day_with_target_hours_and_pto_sick_h: number
          overcapacity_day_with_target_hours_and_pto_training_h: number
          overcapacity_day_with_target_hours_and_pto_vacation_h: number
          overcapacity_day_with_target_hours_h: number
          overcapacity_day_without_target_hours_h: number
          overcapacity_week_h: number
          warning_day_active: boolean
          warning_week_active: boolean
        }
        Insert: {
          id: string
          overcapacity_day_with_target_hours_and_pto_accident_h?: number
          overcapacity_day_with_target_hours_and_pto_compensation_h?: number
          overcapacity_day_with_target_hours_and_pto_freetime_h?: number
          overcapacity_day_with_target_hours_and_pto_holiday_h?: number
          overcapacity_day_with_target_hours_and_pto_military_cs_h?: number
          overcapacity_day_with_target_hours_and_pto_other_h?: number
          overcapacity_day_with_target_hours_and_pto_parental_leave_h?: number
          overcapacity_day_with_target_hours_and_pto_sick_h?: number
          overcapacity_day_with_target_hours_and_pto_training_h?: number
          overcapacity_day_with_target_hours_and_pto_vacation_h?: number
          overcapacity_day_with_target_hours_h?: number
          overcapacity_day_without_target_hours_h?: number
          overcapacity_week_h?: number
          warning_day_active?: boolean
          warning_week_active?: boolean
        }
        Update: {
          id?: string
          overcapacity_day_with_target_hours_and_pto_accident_h?: number
          overcapacity_day_with_target_hours_and_pto_compensation_h?: number
          overcapacity_day_with_target_hours_and_pto_freetime_h?: number
          overcapacity_day_with_target_hours_and_pto_holiday_h?: number
          overcapacity_day_with_target_hours_and_pto_military_cs_h?: number
          overcapacity_day_with_target_hours_and_pto_other_h?: number
          overcapacity_day_with_target_hours_and_pto_parental_leave_h?: number
          overcapacity_day_with_target_hours_and_pto_sick_h?: number
          overcapacity_day_with_target_hours_and_pto_training_h?: number
          overcapacity_day_with_target_hours_and_pto_vacation_h?: number
          overcapacity_day_with_target_hours_h?: number
          overcapacity_day_without_target_hours_h?: number
          overcapacity_week_h?: number
          warning_day_active?: boolean
          warning_week_active?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "employee_capacity_settings_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "employees"
            referencedColumns: ["user_id"]
          },
        ]
      }
      employees: {
        Row: {
          access: Json | null
          active_time_report: boolean
          allow_work_during_pto: boolean
          bexio_user_id: number | null
          clickup_user_id: string | null
          email: string
          inactive: boolean
          max_work_end: string
          max_working_hours: number
          min_work_start: string
          name: string
          profile_picture_url: string | null
          user_id: string
        }
        Insert: {
          access?: Json | null
          active_time_report?: boolean
          allow_work_during_pto?: boolean
          bexio_user_id?: number | null
          clickup_user_id?: string | null
          email: string
          inactive?: boolean
          max_work_end?: string
          max_working_hours?: number
          min_work_start?: string
          name: string
          profile_picture_url?: string | null
          user_id?: string
        }
        Update: {
          access?: Json | null
          active_time_report?: boolean
          allow_work_during_pto?: boolean
          bexio_user_id?: number | null
          clickup_user_id?: string | null
          email?: string
          inactive?: boolean
          max_work_end?: string
          max_working_hours?: number
          min_work_start?: string
          name?: string
          profile_picture_url?: string | null
          user_id?: string
        }
        Relationships: []
      }
      error_notifications: {
        Row: {
          created_at: string
          id: number
          last_sent: string
          message_hash: string
        }
        Insert: {
          created_at?: string
          id?: number
          last_sent?: string
          message_hash: string
        }
        Update: {
          created_at?: string
          id?: number
          last_sent?: string
          message_hash?: string
        }
        Relationships: []
      }
      global_settings: {
        Row: {
          false_timeentries_list_id: string | null
          freetime_list_id: string | null
          fridge_club_list_id: string | null
          general_hourly_rate: number
          hr_list_id: string | null
          id: number
          pm_list_id: string | null
          pto_list_id: string | null
          webhooks_active: boolean
        }
        Insert: {
          false_timeentries_list_id?: string | null
          freetime_list_id?: string | null
          fridge_club_list_id?: string | null
          general_hourly_rate?: number
          hr_list_id?: string | null
          id?: number
          pm_list_id?: string | null
          pto_list_id?: string | null
          webhooks_active?: boolean
        }
        Update: {
          false_timeentries_list_id?: string | null
          freetime_list_id?: string | null
          fridge_club_list_id?: string | null
          general_hourly_rate?: number
          hr_list_id?: string | null
          id?: number
          pm_list_id?: string | null
          pto_list_id?: string | null
          webhooks_active?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "global_settings_false_timeentries_list_id_fkey"
            columns: ["false_timeentries_list_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["clickup_list_id"]
          },
          {
            foreignKeyName: "global_settings_pto_list_id_fkey"
            columns: ["pto_list_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["clickup_list_id"]
          },
          {
            foreignKeyName: "public_global_settings_freetime_list_id_fkey"
            columns: ["freetime_list_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["clickup_list_id"]
          },
          {
            foreignKeyName: "public_global_settings_hr_list_id_fkey"
            columns: ["hr_list_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["clickup_list_id"]
          },
          {
            foreignKeyName: "public_global_settings_pm_list_id_fkey"
            columns: ["pm_list_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["clickup_list_id"]
          },
        ]
      }
      projects: {
        Row: {
          bexio_archived: boolean
          bexio_project_id: number | null
          clickup_archived: boolean
          clickup_end: number | null
          clickup_folder_id: string | null
          clickup_list_id: string | null
          clickup_name: string | null
          clickup_start: number | null
          clickup_user_id: string | null
          deleted: boolean
          dropbox_folder_path: string | null
          id: number
          is_client_project: boolean
          name: string | null
        }
        Insert: {
          bexio_archived?: boolean
          bexio_project_id?: number | null
          clickup_archived?: boolean
          clickup_end?: number | null
          clickup_folder_id?: string | null
          clickup_list_id?: string | null
          clickup_name?: string | null
          clickup_start?: number | null
          clickup_user_id?: string | null
          deleted?: boolean
          dropbox_folder_path?: string | null
          id?: number
          is_client_project?: boolean
          name?: string | null
        }
        Update: {
          bexio_archived?: boolean
          bexio_project_id?: number | null
          clickup_archived?: boolean
          clickup_end?: number | null
          clickup_folder_id?: string | null
          clickup_list_id?: string | null
          clickup_name?: string | null
          clickup_start?: number | null
          clickup_user_id?: string | null
          deleted?: boolean
          dropbox_folder_path?: string | null
          id?: number
          is_client_project?: boolean
          name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "projects_clickup_folder_id_fkey"
            columns: ["clickup_folder_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["clickup_folder_id"]
          },
          {
            foreignKeyName: "projects_clickup_user_id_fkey"
            columns: ["clickup_user_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["clickup_user_id"]
          },
        ]
      }
      pto_credits: {
        Row: {
          created_at: string
          hours: number
          user_id: string
          year: number
        }
        Insert: {
          created_at?: string
          hours: number
          user_id: string
          year: number
        }
        Update: {
          created_at?: string
          hours?: number
          user_id?: string
          year?: number
        }
        Relationships: [
          {
            foreignKeyName: "pto_credits_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["user_id"]
          },
        ]
      }
      sync_operations: {
        Row: {
          attempts: number
          created_at: string
          entity: string
          entity_id: string
          id: string
          last_attempt: string | null
          operation: string
          origin: string
          payload: Json | null
          status: string
          target_id: string | null
        }
        Insert: {
          attempts?: number
          created_at?: string
          entity: string
          entity_id: string
          id?: string
          last_attempt?: string | null
          operation: string
          origin: string
          payload?: Json | null
          status?: string
          target_id?: string | null
        }
        Update: {
          attempts?: number
          created_at?: string
          entity?: string
          entity_id?: string
          id?: string
          last_attempt?: string | null
          operation?: string
          origin?: string
          payload?: Json | null
          status?: string
          target_id?: string | null
        }
        Relationships: []
      }
      tasks: {
        Row: {
          bexio_work_package_id: number | null
          clickup_archived: boolean
          clickup_date_closed: number | null
          clickup_due_date: number | null
          clickup_list_id: string | null
          clickup_status: string | null
          clickup_task_description: string | null
          clickup_task_id: string | null
          clickup_time_estimate: number | null
          clickup_time_spent: number
          clickup_url: string | null
          clickup_user_id: string | null
          id: number
          is_client_task: boolean
          name: string
        }
        Insert: {
          bexio_work_package_id?: number | null
          clickup_archived?: boolean
          clickup_date_closed?: number | null
          clickup_due_date?: number | null
          clickup_list_id?: string | null
          clickup_status?: string | null
          clickup_task_description?: string | null
          clickup_task_id?: string | null
          clickup_time_estimate?: number | null
          clickup_time_spent?: number
          clickup_url?: string | null
          clickup_user_id?: string | null
          id?: number
          is_client_task?: boolean
          name?: string
        }
        Update: {
          bexio_work_package_id?: number | null
          clickup_archived?: boolean
          clickup_date_closed?: number | null
          clickup_due_date?: number | null
          clickup_list_id?: string | null
          clickup_status?: string | null
          clickup_task_description?: string | null
          clickup_task_id?: string | null
          clickup_time_estimate?: number | null
          clickup_time_spent?: number
          clickup_url?: string | null
          clickup_user_id?: string | null
          id?: number
          is_client_task?: boolean
          name?: string
        }
        Relationships: [
          {
            foreignKeyName: "tasks_clickup_list_id_fkey"
            columns: ["clickup_list_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["clickup_list_id"]
          },
          {
            foreignKeyName: "tasks_clickup_user_id_fkey"
            columns: ["clickup_user_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["clickup_user_id"]
          },
        ]
      }
      time_corrections: {
        Row: {
          created_at: string
          date: string
          hours: number
          id: number
          is_overtime: boolean
          is_pto: boolean
          note: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          date: string
          hours?: number
          id?: number
          is_overtime?: boolean
          is_pto?: boolean
          note?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          date?: string
          hours?: number
          id?: number
          is_overtime?: boolean
          is_pto?: boolean
          note?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "time_corrections_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["user_id"]
          },
        ]
      }
      time_entries: {
        Row: {
          bexio_timesheet_id: number | null
          billable: boolean | null
          billable_amount: number | null
          clickup_description: string | null
          clickup_duration: number | null
          clickup_end: number | null
          clickup_start: number | null
          clickup_task_id: string | null
          clickup_task_tag: string | null
          clickup_time_entry_id: string | null
          clickup_user_id: string | null
          error_reminder_sent_date: string | null
          id: number
        }
        Insert: {
          bexio_timesheet_id?: number | null
          billable?: boolean | null
          billable_amount?: number | null
          clickup_description?: string | null
          clickup_duration?: number | null
          clickup_end?: number | null
          clickup_start?: number | null
          clickup_task_id?: string | null
          clickup_task_tag?: string | null
          clickup_time_entry_id?: string | null
          clickup_user_id?: string | null
          error_reminder_sent_date?: string | null
          id?: number
        }
        Update: {
          bexio_timesheet_id?: number | null
          billable?: boolean | null
          billable_amount?: number | null
          clickup_description?: string | null
          clickup_duration?: number | null
          clickup_end?: number | null
          clickup_start?: number | null
          clickup_task_id?: string | null
          clickup_task_tag?: string | null
          clickup_time_entry_id?: string | null
          clickup_user_id?: string | null
          error_reminder_sent_date?: string | null
          id?: number
        }
        Relationships: [
          {
            foreignKeyName: "time_entries_clickup_task_id_fkey"
            columns: ["clickup_task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["clickup_task_id"]
          },
          {
            foreignKeyName: "time_entries_clickup_task_tag_fkey"
            columns: ["clickup_task_tag"]
            isOneToOne: false
            referencedRelation: "business_activities"
            referencedColumns: ["clickup_task_tag"]
          },
          {
            foreignKeyName: "time_entries_clickup_user_id_fkey"
            columns: ["clickup_user_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["clickup_user_id"]
          },
        ]
      }
      working_hours: {
        Row: {
          created_at: string
          date: string
          hours: number
          user_id: string
        }
        Insert: {
          created_at?: string
          date: string
          hours?: number
          user_id: string
        }
        Update: {
          created_at?: string
          date?: string
          hours?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "working_hours_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["user_id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          level: number | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objects_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      prefixes: {
        Row: {
          bucket_id: string
          created_at: string | null
          level: number
          name: string
          updated_at: string | null
        }
        Insert: {
          bucket_id: string
          created_at?: string | null
          level?: number
          name: string
          updated_at?: string | null
        }
        Update: {
          bucket_id?: string
          created_at?: string | null
          level?: number
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "prefixes_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_parts_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "s3_multipart_uploads_parts_upload_id_fkey"
            columns: ["upload_id"]
            isOneToOne: false
            referencedRelation: "s3_multipart_uploads"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_prefixes: {
        Args: {
          _bucket_id: string
          _name: string
        }
        Returns: undefined
      }
      can_insert_object: {
        Args: {
          bucketid: string
          name: string
          owner: string
          metadata: Json
        }
        Returns: undefined
      }
      delete_prefix: {
        Args: {
          _bucket_id: string
          _name: string
        }
        Returns: boolean
      }
      extension: {
        Args: {
          name: string
        }
        Returns: string
      }
      filename: {
        Args: {
          name: string
        }
        Returns: string
      }
      foldername: {
        Args: {
          name: string
        }
        Returns: string[]
      }
      get_level: {
        Args: {
          name: string
        }
        Returns: number
      }
      get_prefix: {
        Args: {
          name: string
        }
        Returns: string
      }
      get_prefixes: {
        Args: {
          name: string
        }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
        }
        Returns: {
          key: string
          id: string
          created_at: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          start_after?: string
          next_token?: string
        }
        Returns: {
          name: string
          id: string
          metadata: Json
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_legacy_v1: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_v1_optimised: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
      search_v2: {
        Args: {
          prefix: string
          bucket_name: string
          limits?: number
          levels?: number
          start_after?: string
        }
        Returns: {
          key: string
          name: string
          id: string
          updated_at: string
          created_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

